using DispatchR.Requests.Send;
using DNTPersianUtils.Core;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Text.Json.Serialization;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Constants;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.Exceptions;
using Zify.Settlement.Application.Domain.Helpers;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Infrastructure.Configurations;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.OrderDetails;

public sealed record AddOrderDetailsResponse(List<OrderDetailResponse> OrderDetails);

public sealed class AddOrderDetailsController : ApiControllerBase
{
    [HttpPost("{orderId:guid}/add-order-details")]
    [Authorize("write")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AddOrderDetails(
        [FromRoute] Guid orderId,
        [FromBody] AddOrderDetailsCommand request)
    {
        request.OrderId = orderId;
        var result = await Mediator.Send(request, HttpContext.RequestAborted);
        return result.Match(Ok, Problem);
    }
}

public sealed record AddOrderDetailsCommand(List<OrderDetailItem> Items)
    : IRequest<AddOrderDetailsCommand, Task<ErrorOr<AddOrderDetailsResponse>>>
{
    [JsonIgnore]
    public Guid OrderId { get; set; }
}

public sealed record OrderDetailItem(
    decimal Amount,
    string Iban,
    string? Description,
    string? Mobile,
    string? NationalId);

public sealed class AddOrderDetailsCommandHandler(
    ApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    IEzPayService ezPayService)
    : IRequestHandler<AddOrderDetailsCommand, Task<ErrorOr<AddOrderDetailsResponse>>>
{
    public async Task<ErrorOr<AddOrderDetailsResponse>> Handle(
        AddOrderDetailsCommand request,
        CancellationToken cancellationToken)
    {
        var order = await dbContext.Orders
            .AsTracking()
            .Where(o => o.Id == request.OrderId)
            .FirstOrDefaultAsync(cancellationToken);

        if (order is null)
            return Error.NotFound(description: "کد درخواست نامعتبر می‌باشد");

        if (order.Status != OrderStatus.Draft)
            return Error.Forbidden(description: "با توجه به وضعیت درخواست افزودن تسویه امکان پذیر نیست");

        var userConfig = await dbContext.UserConfigs
            .AsNoTracking()
            .Where(uc => uc.UserId == currentUserService.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        var createdOrderDetails = new List<OrderDetailResponse>();

        foreach (var item in request.Items)
        {
            var fullNameOrIban = await TryValidateIbanAndAccountStatus(item.Iban);
            if (fullNameOrIban.IsError)
                return fullNameOrIban.Errors;

            var orderDetail = OrderDetail.Create(
                Iban.Of(item.Iban),
                item.Amount,
                userConfig.CalculateWage(item.Amount),
                item.NationalId,
                item.Mobile,
                item.Description);

            order.AddDetail(orderDetail);
            createdOrderDetails.Add(OrderDetailResponse.FromDomain(orderDetail, fullNameOrIban.Value));
        }

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        return result > 0
            ? new AddOrderDetailsResponse(createdOrderDetails)
            : Error.Failure(description: "خطا در ثبت اطلاعات");
    }

    private async Task<ErrorOr<string>> TryValidateIbanAndAccountStatus(string iban)
    {
        var inquiryIban = await ezPayService.InquiryIban(iban);
        var fullNameOrIban = inquiryIban.Value?.AccountOwners.FirstOrDefault()?.FullName ?? iban;

        if (!inquiryIban.IsError && (!inquiryIban.Value?.IsActive ?? true))
            return Error.Forbidden(description:
                $"حساب بانکی مربوط به شبای {fullNameOrIban} مسدود می‌باشد");

        return fullNameOrIban;
    }
}

public sealed class AddOrderDetailsCommandValidator : AbstractValidator<AddOrderDetailsCommand>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ICurrentUserService _currentUserService;

    public AddOrderDetailsCommandValidator(ApplicationDbContext dbContext, ICurrentUserService currentUserService)
    {
        _dbContext = dbContext;
        _currentUserService = currentUserService;

        WhenAsync(UserHasSimplePlan,
            () => throw new SimplePlanSettlementAdditionForbiddenException());

        WhenAsync(NotExceedMaximumOrderDetailsCount,
            () => throw new ReachedMaximumOrderDetailsCountException(UserCheckOptions.GetMaxSettlementCountPerRequest));

        RuleFor(x => x.Items)
            .NotEmpty().WithMessage("لیست آیتم‌ها نمی‌تواند خالی باشد");

        RuleForEach(x => x.Items)
            .SetValidator(new OrderDetailItemValidator(dbContext, currentUserService));

        RuleFor(x => x.OrderId).NotEmpty();
    }

    private async Task<bool> UserHasSimplePlan(AddOrderDetailsCommand _, CancellationToken ct)
    {
        var plan = await _dbContext.UserConfigs
            .AsNoTracking()
            .Where(x => x.UserId == _currentUserService.UserId)
            .Select(x => x.PlanType)
            .FirstOrDefaultAsync(ct);

        return plan == SettlementPlanType.Basic;
    }

    private async Task<bool> NotExceedMaximumOrderDetailsCount(AddOrderDetailsCommand command, CancellationToken ct)
    {
        var detailsCount = await _dbContext.OrderDetails
            .AsNoTracking()
            .Where(x => x.OrderId == command.OrderId)
            .CountAsync(ct);

        return detailsCount + command.Items.Count <= UserCheckOptions.GetMaxSettlementCountPerRequest;
    }
}

public class OrderDetailItemValidator : AbstractValidator<OrderDetailItem>
{
    public OrderDetailItemValidator(ApplicationDbContext dbContext, ICurrentUserService currentUserService)
    {
        RuleFor(x => x.Amount)
            .NotEmpty().WithMessage("مبلغ اجباری است")
            .GreaterThan(UserCheckOptions.GetMinSettlementAmount)
            .WithMessage("مبلغ تسویه کمتر از حد مجاز")
            .MustAsync(LessThanMaxSettlementAmount)
            .WithMessage("مبلغ تسویه بیشتر از حد مجاز");

        RuleFor(x => x.Iban).NotEmpty();

        RuleFor(x => x.Description)
            .MaximumLength(50).WithMessage("متن توضیحات بیشتر از ۵۰ کارکتر نمی‌تواند باشد");

        WhenAsync(UserIsCritical, () =>
        {
            RuleFor(x => x.Mobile)
                .NotEmpty().WithMessage(ErrorMessages.MobileNumberRequired)
                .Must(x => x.IsValidIranianMobileNumber()).WithMessage(ErrorMessages.InvalidNationalCode);

            RuleFor(x => x.NationalId)
                .NotEmpty().WithMessage(ErrorMessages.NationalCodeRequired)
                .Must(x => x.IsValidIranianNationalCode()).WithMessage(ErrorMessages.InvalidNationalCode);
        });
        return;

        async Task<bool> LessThanMaxSettlementAmount(decimal amount, CancellationToken ct)
        {
            long max = await dbContext.UserConfigs.AsNoTracking()
                .Where(x => x.UserId == currentUserService.UserId)
                .Select(x => x.MaxSettlementAmount)
                .FirstOrDefaultAsync(ct);
            if (max == 0) max = UserCheckOptions.GetMaxSettlementAmount;
            return amount <= max;
        }
        async Task<bool> UserIsCritical(OrderDetailItem _, CancellationToken ct)
        {
            return await dbContext.UserConfigs
                .AsNoTracking()
                .Where(x => x.UserId == currentUserService.UserId)
                .Select(x => x.IsCritical)
                .FirstOrDefaultAsync(ct);
        }
    }
}